import { CookieConsent } from "@/components/cookie-consent";
import { PortavioLayout } from "@/components/portavio-layout";
import Scroll from "@/components/scroll";
import { ThemeProvider } from "@/components/theme-provider";
import { ProfilePictureProvider } from "@/hooks/use-profile-picture";
import type { Metadata } from "next";
import { Instrument_Sans } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/sonner";

const instrumentSans = Instrument_Sans({
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Portavio",
  description:
    "Conectează-te automat cu brokerul tău și urmărește-ți portofoliul în detaliu.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={instrumentSans.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange={false}
        >
          <ProfilePictureProvider>
            <Scroll />
            <PortavioLayout>{children}</PortavioLayout>
            <CookieConsent />
            <Toaster richColors closeButton />
          </ProfilePictureProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
