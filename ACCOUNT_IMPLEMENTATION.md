# Account Management Implementation

This document describes the implementation of the account management page with the card layout design as requested.

## Features Implemented

### 1. Database Schema

- **Profile Pictures Table**: New table `profile_pictures` to store base64 encoded images
- **User-Account Relationships**: Proper relationships between user and account tables
- **Provider Detection**: Logic to determine account capabilities based on provider type

### 2. Account Details Page

- **Server-Side Rendering**: Data fetched in server component and passed to client component
- **Sidebar Navigation**: Left sidebar with menu items (only "Detalii cont" and "Schimbare parolă" are currently active)
- **Profile Picture Upload**: Support for uploading, displaying, and removing profile pictures
- **Provider-Specific Fields**: Email and password changes only available for credential accounts
- **Form Validation**: Comprehensive validation using Zod schemas

### 3. Components Created

- `AccountSidebar`: Navigation sidebar with menu items
- `AccountDetailsForm`: Form for updating user profile information
- `PasswordChangeForm`: Form for changing passwords (credential accounts only)
- `ProfilePictureUpload`: Component for managing profile pictures

### 4. API Routes

- `GET /api/account/data`: Fetch current user account data
- `PUT /api/account/profile`: Update user profile information
- `PUT /api/account/password`: Change user password
- `POST /api/account/profile-picture`: Upload profile picture
- `DELETE /api/account/profile-picture`: Remove profile picture

### 5. Architecture

- **Server Component**: `src/app/profile/page.tsx` - Fetches data server-side
- **Client Component**: `src/app/profile/ClientPage.tsx` - Handles user interactions
- **Proper Data Flow**: Server fetches initial data, client handles updates via API routes

## Database Migration

Before using the account management features, you need to run the database migration:

```bash
# Run the profile pictures table creation script
psql -h your_host -U your_user -d your_database -f scripts/create-profile-pictures-table.sql
```

Or execute the SQL directly in your database:

```sql
-- The script is located at: scripts/create-profile-pictures-table.sql
```

## Provider-Specific Logic

The implementation includes logic to handle different authentication providers:

### Credential Accounts (Email/Password)

- ✅ Can change email
- ✅ Can change password
- ✅ Can change username
- ✅ Can change name
- ✅ Can upload profile picture

### Social Accounts (Google, etc.)

- ❌ Cannot change email (managed by provider)
- ❌ Cannot change password (managed by provider)
- ✅ Can change username
- ✅ Can change name
- ✅ Can upload profile picture

## Image Handling

The system supports two types of profile images:

1. **External URLs**: Images from social providers (Google, etc.)
2. **Base64 Images**: Custom uploaded images stored in the database

The `getImageSourceType()` function determines the image type and shows appropriate controls.

## File Structure

```
src/
├── app/
│   ├── api/account/
│   │   ├── data/route.ts
│   │   ├── profile/route.ts
│   │   ├── password/route.ts
│   │   └── profile-picture/route.ts
│   └── my-account/
│       ├── page.tsx (Server Component)
│       └── ClientPage.tsx (Client Component)
├── components/account/
│   ├── account-sidebar.tsx
│   ├── account-details-form.tsx
│   ├── password-change-form.tsx
│   └── profile-picture-upload.tsx
├── lib/
│   └── account-schemas.ts
├── utils/db/
│   └── account-queries.ts
└── scripts/
    └── create-profile-pictures-table.sql
```

## Usage

1. Navigate to `/profile` when logged in
2. Use the sidebar to switch between "Detalii cont" and "Schimbare parolă"
3. Upload profile pictures by dragging/dropping or clicking the upload area
4. Update profile information (availability depends on account provider)
5. Change password (only available for credential accounts)

## Security Features

- **Provider Validation**: Fields are disabled based on account provider
- **File Validation**: Profile pictures are validated for type and size
- **Password Verification**: Current password required for password changes
- **Session Validation**: All API routes require valid authentication

## Responsive Design

The layout is responsive and works on:

- Desktop: Sidebar on the left, content on the right
- Mobile/Tablet: Sidebar stacks above content

## Error Handling

- Comprehensive error messages in Romanian
- Form validation with field-specific errors
- API error handling with user-friendly messages
- Loading states for all async operations

## Future Enhancements

The sidebar includes placeholder items for future features:

- Notificări (Notifications)
- Abonament (Subscription)
- Setări (Settings)
- Securitate (Security/2FA)
- Documente (Documents)
- Suport (Support)

These are marked as "În curând" (Coming Soon) and are disabled.
