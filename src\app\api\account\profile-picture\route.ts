import { auth } from "@/lib/auth";
import { profilePictureSchema } from "@/lib/account-schemas";
import {
  uploadProfilePicture,
  deleteProfilePicture,
} from "@/utils/db/account-queries";
import { headers } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

async function fileToBase64(file: File): Promise<string> {
  const bytes = await file.arrayBuffer();
  const buffer = Buffer.from(bytes);
  return `data:${file.type};base64,${buffer.toString("base64")}`;
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Nu ești autentificat" },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    const file = formData.get("file") as File;

    if (!file) {
      return NextResponse.json(
        { error: "Nu a fost selectat niciun fișier" },
        { status: 400 }
      );
    }

    const validation = profilePictureSchema.safeParse({ file });
    console.log(validation);
    if (!validation.success) {
      return NextResponse.json(
        {
          error: "Fișierul nu este valid",
          details: validation.error.errors,
        },
        { status: 400 }
      );
    }

    const base64Data = await fileToBase64(file);

    const profilePicture = await uploadProfilePicture(
      session.user.id,
      base64Data,
      file.type,
      file.size
    );

    return NextResponse.json({
      success: true,
      message: "Imaginea de profil a fost încărcată cu succes",
      profilePicture,
    });
  } catch (error) {
    console.error("Error uploading profile picture:", error);

    if (error instanceof Error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(
      { error: "A apărut o eroare la încărcarea imaginii" },
      { status: 500 }
    );
  }
}

export async function DELETE() {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Nu ești autentificat" },
        { status: 401 }
      );
    }

    await deleteProfilePicture(session.user.id);

    return NextResponse.json({
      success: true,
      message: "Imaginea de profil a fost ștearsă cu succes",
    });
  } catch (error) {
    console.error("Error deleting profile picture:", error);

    if (error instanceof Error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(
      { error: "A apărut o eroare la ștergerea imaginii" },
      { status: 500 }
    );
  }
}
