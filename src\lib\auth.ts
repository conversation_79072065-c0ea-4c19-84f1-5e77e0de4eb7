import { betterAuth } from "better-auth";
import { nextCookies } from "better-auth/next-js";
import { username } from "better-auth/plugins";
import { Pool } from "pg";

export const auth = betterAuth({
  database: new Pool({
    database: process.env.DATABASE_NAME!,
    host: process.env.DATABASE_HOST!,
    password: process.env.DATABASE_PASSWORD!,
    port: Number(process.env.DATABASE_PORT!),
    user: process.env.DATABASE_USER!,
  }),
  secret: process.env.BETTER_AUTH_SECRET!,
  baseURL: process.env.BETTER_AUTH_URL!,
  trustedOrigins: ["http://localhost:3000", "https://portavio-dev.testbox.ro/"],
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false,
  },
  socialProviders: {
    google: {
      prompt: "select_account",
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
    },
  },
  plugins: [
    username({
      minUsernameLength: 3,
      maxUsernameLength: 30,
      usernameValidator: (username) => {
        // Only allow alphanumeric characters, underscores, and dots
        const validPattern = /^[a-zA-Z0-9_.]+$/;
        if (!validPattern.test(username)) {
          return false;
        }
        // Prevent reserved usernames
        const reservedUsernames = ["admin", "root", "system", "api", "www"];
        return !reservedUsernames.includes(username.toLowerCase());
      },
    }),
    nextCookies(),
  ],
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
  },
  user: {
    additionalFields: {
      username: {
        type: "string",
        required: false,
        unique: true,
      },
      displayUsername: {
        type: "string",
        required: false,
      },
    },
  },
  account: {
    accountLinking: {
      enabled: false,
      allowDifferentEmails: false,
    },
  },
});

export type Session = typeof auth.$Infer.Session;
export type User = typeof auth.$Infer.Session.user;
